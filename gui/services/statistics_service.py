"""
Service for generating statistics and chart data for admin dashboard.
"""
from datetime import datetime, timedelta
from facial_recognition_system.local_database import get_connection


def get_attendance_statistics(days=30, class_id=None, subject_id=None):
    """
    Get attendance statistics for charts.
    
    Args:
        days: Number of days to look back
        class_id: Filter by specific class
        subject_id: Filter by specific subject
        
    Returns:
        dict: Statistics data for charts
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        # Base query conditions and table joins
        conditions = ["p.date >= ? AND p.date <= ?"]
        params = [start_date_str, end_date_str]
        from_clause = "presences p"

        if class_id:
            conditions.append("p.class_id = ?")
            params.append(class_id)
        if subject_id:
            # Check if subject_id is actually a subject name (for dropdown filtering)
            if isinstance(subject_id, str) and not subject_id.isdigit():
                from_clause = "presences p JOIN matieres m ON p.subject_id = m.id"
                conditions.append("m.name = ?")
                params.append(subject_id)
            else:
                conditions.append("p.subject_id = ?")
                params.append(subject_id)

        where_clause = " AND ".join(conditions)

        # Daily attendance rates
        cursor.execute(f"""
            SELECT p.date,
                   COUNT(*) as total_records,
                   SUM(CASE WHEN p.status = 1 THEN 1 ELSE 0 END) as present_count,
                   SUM(CASE WHEN p.status = 0 THEN 1 ELSE 0 END) as absent_count,
                   ROUND(AVG(CASE WHEN p.status = 1 THEN 100.0 ELSE 0.0 END), 2) as attendance_rate
            FROM {from_clause}
            WHERE {where_clause}
            GROUP BY p.date
            ORDER BY p.date
        """, params)
        
        daily_stats = [dict(row) for row in cursor.fetchall()]
        
        # Attendance by class
        cursor.execute(f"""
            SELECT c.name as class_name,
                   COUNT(*) as total_records,
                   SUM(CASE WHEN p.status = 1 THEN 1 ELSE 0 END) as present_count,
                   ROUND(AVG(CASE WHEN p.status = 1 THEN 100.0 ELSE 0.0 END), 2) as attendance_rate
            FROM {from_clause}
            LEFT JOIN classes c ON p.class_id = c.id
            WHERE {where_clause}
            GROUP BY c.id, c.name
            ORDER BY attendance_rate DESC
        """, params)

        class_stats = [dict(row) for row in cursor.fetchall()]

        # Attendance by subject
        cursor.execute(f"""
            SELECT m.name as subject_name,
                   COUNT(*) as total_records,
                   SUM(CASE WHEN p.status = 1 THEN 1 ELSE 0 END) as present_count,
                   ROUND(AVG(CASE WHEN p.status = 1 THEN 100.0 ELSE 0.0 END), 2) as attendance_rate
            FROM {from_clause}
            LEFT JOIN matieres m ON p.subject_id = m.id
            WHERE {where_clause}
            GROUP BY m.id, m.name
            ORDER BY attendance_rate DESC
        """, params)

        subject_stats = [dict(row) for row in cursor.fetchall()]
        
        # Overall statistics
        cursor.execute(f"""
            SELECT COUNT(*) as total_records,
                   SUM(CASE WHEN p.status = 1 THEN 1 ELSE 0 END) as total_present,
                   SUM(CASE WHEN p.status = 0 THEN 1 ELSE 0 END) as total_absent,
                   ROUND(AVG(CASE WHEN p.status = 1 THEN 100.0 ELSE 0.0 END), 2) as overall_rate
            FROM {from_clause}
            WHERE {where_clause}
        """, params)
        
        overall_stats = dict(cursor.fetchone() or {})
        
        return {
            'daily_stats': daily_stats,
            'class_stats': class_stats,
            'subject_stats': subject_stats,
            'overall_stats': overall_stats,
            'date_range': {
                'start': start_date_str,
                'end': end_date_str
            }
        }
        
    except Exception as e:
        print(f"Error getting attendance statistics: {e}")
        return {
            'daily_stats': [],
            'class_stats': [],
            'subject_stats': [],
            'overall_stats': {},
            'date_range': {'start': '', 'end': ''}
        }


def get_quiz_statistics(days=30, class_id=None, subject_id=None):
    """
    Get quiz statistics for charts.
    
    Args:
        days: Number of days to look back
        class_id: Filter by specific class
        subject_id: Filter by specific subject
        
    Returns:
        dict: Quiz statistics data for charts
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # Check if quiz tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='quiz'")
        if not cursor.fetchone():
            return {
                'daily_stats': [],
                'class_stats': [],
                'subject_stats': [],
                'score_distribution': [],
                'overall_stats': {}
            }
        
        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        # Base query conditions and table joins
        conditions = ["DATE(s.submitted_at) >= ? AND DATE(s.submitted_at) <= ?"]
        params = [start_date_str, end_date_str]
        from_clause = "soumissions_quiz s JOIN quiz q ON s.quiz_id = q.id"

        if class_id:
            conditions.append("q.class_id = ?")
            params.append(class_id)
        if subject_id:
            # Check if subject_id is actually a subject name (for dropdown filtering)
            if isinstance(subject_id, str) and not subject_id.isdigit():
                from_clause = "soumissions_quiz s JOIN quiz q ON s.quiz_id = q.id JOIN matieres m ON q.subject_id = m.id"
                conditions.append("m.name = ?")
                params.append(subject_id)
            else:
                conditions.append("q.subject_id = ?")
                params.append(subject_id)

        where_clause = " AND ".join(conditions)
        
        # Daily quiz performance
        cursor.execute(f"""
            SELECT DATE(s.submitted_at) as date,
                   COUNT(*) as total_submissions,
                   ROUND(AVG(CAST(s.score AS FLOAT) / CAST(s.total_questions AS FLOAT) * 100), 2) as avg_score,
                   MAX(CAST(s.score AS FLOAT) / CAST(s.total_questions AS FLOAT) * 100) as max_score,
                   MIN(CAST(s.score AS FLOAT) / CAST(s.total_questions AS FLOAT) * 100) as min_score
            FROM {from_clause}
            WHERE {where_clause}
            GROUP BY DATE(s.submitted_at)
            ORDER BY DATE(s.submitted_at)
        """, params)
        
        daily_stats = [dict(row) for row in cursor.fetchall()]
        
        # Quiz performance by class
        cursor.execute(f"""
            SELECT c.name as class_name,
                   COUNT(*) as total_submissions,
                   ROUND(AVG(CAST(s.score AS FLOAT) / CAST(s.total_questions AS FLOAT) * 100), 2) as avg_score
            FROM {from_clause}
            LEFT JOIN classes c ON q.class_id = c.id
            WHERE {where_clause}
            GROUP BY c.id, c.name
            ORDER BY avg_score DESC
        """, params)

        class_stats = [dict(row) for row in cursor.fetchall()]

        # Quiz performance by subject
        cursor.execute(f"""
            SELECT m.name as subject_name,
                   COUNT(*) as total_submissions,
                   ROUND(AVG(CAST(s.score AS FLOAT) / CAST(s.total_questions AS FLOAT) * 100), 2) as avg_score
            FROM {from_clause}
            LEFT JOIN matieres m ON q.subject_id = m.id
            WHERE {where_clause}
            GROUP BY m.id, m.name
            ORDER BY avg_score DESC
        """, params)
        
        subject_stats = [dict(row) for row in cursor.fetchall()]

        # Score distribution
        cursor.execute(f"""
            SELECT
                CASE
                    WHEN CAST(s.score AS FLOAT) / CAST(s.total_questions AS FLOAT) * 100 >= 90 THEN '90-100%'
                    WHEN CAST(s.score AS FLOAT) / CAST(s.total_questions AS FLOAT) * 100 >= 80 THEN '80-89%'
                    WHEN CAST(s.score AS FLOAT) / CAST(s.total_questions AS FLOAT) * 100 >= 70 THEN '70-79%'
                    WHEN CAST(s.score AS FLOAT) / CAST(s.total_questions AS FLOAT) * 100 >= 60 THEN '60-69%'
                    ELSE 'Below 60%'
                END as score_range,
                COUNT(*) as count
            FROM {from_clause}
            WHERE {where_clause}
            GROUP BY score_range
            ORDER BY
                CASE score_range
                    WHEN '90-100%' THEN 1
                    WHEN '80-89%' THEN 2
                    WHEN '70-79%' THEN 3
                    WHEN '60-69%' THEN 4
                    ELSE 5
                END
        """, params)

        score_distribution = [dict(row) for row in cursor.fetchall()]

        # Overall statistics
        cursor.execute(f"""
            SELECT COUNT(*) as total_submissions,
                   COUNT(DISTINCT s.quiz_id) as total_quizzes,
                   COUNT(DISTINCT s.student_name) as total_students,
                   ROUND(AVG(CAST(s.score AS FLOAT) / CAST(s.total_questions AS FLOAT) * 100), 2) as overall_avg_score
            FROM {from_clause}
            WHERE {where_clause}
        """, params)

        overall_stats = dict(cursor.fetchone() or {})

        return {
            'daily_stats': daily_stats,
            'class_stats': class_stats,
            'subject_stats': subject_stats,
            'score_distribution': score_distribution,
            'overall_stats': overall_stats,
            'date_range': {
                'start': start_date_str,
                'end': end_date_str
            }
        }
        
    except Exception as e:
        print(f"Error getting quiz statistics: {e}")
        return {
            'daily_stats': [],
            'class_stats': [],
            'subject_stats': [],
            'overall_stats': {},
            'date_range': {'start': '', 'end': ''}
        }


def get_filter_options():
    """
    Get available filter options for statistics.
    
    Returns:
        dict: Available classes and subjects for filtering
    """
    try:
        conn = get_connection()
        cursor = conn.cursor()
        
        # Get classes
        cursor.execute("SELECT id, name FROM classes ORDER BY name")
        classes = [{'id': row['id'], 'name': row['name']} for row in cursor.fetchall()]
        
        # Get subjects (distinct names only to avoid redundancy)
        cursor.execute("SELECT DISTINCT name FROM matieres ORDER BY name")
        subject_rows = cursor.fetchall()
        subjects = [{'id': row['name'], 'name': row['name']} for row in subject_rows]
        
        return {
            'classes': classes,
            'subjects': subjects
        }
        
    except Exception as e:
        print(f"Error getting filter options: {e}")
        return {
            'classes': [],
            'subjects': []
        }
