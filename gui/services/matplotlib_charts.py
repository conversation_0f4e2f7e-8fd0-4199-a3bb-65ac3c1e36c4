"""
Matplotlib chart service for generating professional-looking charts.
"""
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import pandas as pd
import numpy as np
from datetime import datetime
import os
import io
import base64

# Set style for better-looking charts
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("husl")

# Create charts directory if it doesn't exist
CHARTS_DIR = "gui/static/charts"
os.makedirs(CHARTS_DIR, exist_ok=True)


def create_attendance_line_chart(daily_stats, period_label="30 jours"):
    """
    Create a professional attendance line chart using matplotlib.
    
    Args:
        daily_stats: List of daily attendance statistics
        period_label: Label for the time period
        
    Returns:
        str: Base64 encoded image data
    """
    if not daily_stats:
        return create_no_data_chart("Aucune donnée de présence disponible")
    
    # Prepare data
    dates = []
    rates = []
    
    for stat in daily_stats:
        try:
            date_str = stat.get('date', '')
            if date_str:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                dates.append(date_obj)
                rates.append(stat.get('attendance_rate', 0))
        except:
            continue
    
    if not dates:
        return create_no_data_chart("Aucune donnée de présence disponible")
    
    # Create figure
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # Plot line
    ax.plot(dates, rates, linewidth=3, marker='o', markersize=6, 
            color='#2E8B57', markerfacecolor='white', markeredgewidth=2)
    
    # Customize axes
    ax.set_title(f'Évolution du Taux de Présence ({period_label})', 
                fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('Date', fontsize=12, fontweight='bold')
    ax.set_ylabel('Taux de Présence (%)', fontsize=12, fontweight='bold')
    
    # Format x-axis
    if len(dates) > 10:
        ax.xaxis.set_major_locator(mdates.WeekdayLocator())
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
    else:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
    
    # Set y-axis limits and ticks
    ax.set_ylim(0, 100)
    ax.set_yticks(range(0, 101, 20))
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{int(x)}%'))
    
    # Add grid
    ax.grid(True, alpha=0.3)
    
    # Rotate x-axis labels
    plt.xticks(rotation=45)
    
    # Tight layout
    plt.tight_layout()
    
    # Convert to base64
    return fig_to_base64(fig)


def create_attendance_bar_chart(class_stats):
    """
    Create a professional attendance bar chart by class.
    
    Args:
        class_stats: List of class attendance statistics
        
    Returns:
        str: Base64 encoded image data
    """
    if not class_stats:
        return create_no_data_chart("Aucune donnée par classe disponible")
    
    # Limit to top 8 classes for readability
    class_stats = class_stats[:8]
    
    # Prepare data
    class_names = [stat.get('class_name', f'Classe {i+1}')[:15] for i, stat in enumerate(class_stats)]
    rates = [stat.get('attendance_rate', 0) for stat in class_stats]
    
    # Create figure
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # Create bars with gradient colors
    colors = plt.cm.viridis(np.linspace(0.3, 0.9, len(class_names)))
    bars = ax.bar(class_names, rates, color=colors, alpha=0.8, edgecolor='white', linewidth=1)
    
    # Add value labels on bars
    for bar, rate in zip(bars, rates):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # Customize axes
    ax.set_title('Taux de Présence par Classe', fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('Classes', fontsize=12, fontweight='bold')
    ax.set_ylabel('Taux de Présence (%)', fontsize=12, fontweight='bold')
    
    # Set y-axis limits and ticks
    ax.set_ylim(0, 100)
    ax.set_yticks(range(0, 101, 20))
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{int(x)}%'))
    
    # Add grid
    ax.grid(True, alpha=0.3, axis='y')
    
    # Rotate x-axis labels
    plt.xticks(rotation=45, ha='right')
    
    # Tight layout
    plt.tight_layout()
    
    return fig_to_base64(fig)


def create_attendance_pie_chart(overall_stats):
    """
    Create a professional attendance pie chart.
    
    Args:
        overall_stats: Overall attendance statistics
        
    Returns:
        str: Base64 encoded image data
    """
    total_present = overall_stats.get('total_present', 0)
    total_absent = overall_stats.get('total_absent', 0)
    
    if total_present == 0 and total_absent == 0:
        return create_no_data_chart("Aucune donnée de présence disponible")
    
    # Prepare data
    labels = []
    sizes = []
    colors = []
    
    if total_present > 0:
        labels.append(f'Présent\n{total_present}')
        sizes.append(total_present)
        colors.append('#2E8B57')
    
    if total_absent > 0:
        labels.append(f'Absent\n{total_absent}')
        sizes.append(total_absent)
        colors.append('#DC143C')
    
    # Create figure
    fig, ax = plt.subplots(figsize=(8, 8))
    
    # Create pie chart
    wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%',
                                     startangle=90, textprops={'fontsize': 12, 'fontweight': 'bold'})
    
    # Customize
    ax.set_title('Répartition Présence/Absence', fontsize=16, fontweight='bold', pad=20)
    
    # Equal aspect ratio ensures that pie is drawn as a circle
    ax.axis('equal')
    
    # Tight layout
    plt.tight_layout()
    
    return fig_to_base64(fig)


def create_quiz_line_chart(daily_stats, period_label="30 jours"):
    """
    Create a professional quiz performance line chart.
    
    Args:
        daily_stats: List of daily quiz statistics
        period_label: Label for the time period
        
    Returns:
        str: Base64 encoded image data
    """
    if not daily_stats:
        return create_no_data_chart("Aucune donnée de quiz disponible")
    
    # Prepare data
    dates = []
    avg_scores = []
    
    for stat in daily_stats:
        try:
            date_str = stat.get('date', '')
            if date_str:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                dates.append(date_obj)
                avg_scores.append(stat.get('avg_score', 0))
        except:
            continue
    
    if not dates:
        return create_no_data_chart("Aucune donnée de quiz disponible")
    
    # Create figure
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # Plot line
    ax.plot(dates, avg_scores, linewidth=3, marker='o', markersize=6,
            color='#8A2BE2', markerfacecolor='white', markeredgewidth=2)
    
    # Customize axes
    ax.set_title(f'Évolution des Scores de Quiz ({period_label})', 
                fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('Date', fontsize=12, fontweight='bold')
    ax.set_ylabel('Score Moyen (%)', fontsize=12, fontweight='bold')
    
    # Format x-axis
    if len(dates) > 10:
        ax.xaxis.set_major_locator(mdates.WeekdayLocator())
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
    else:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%d/%m'))
    
    # Set y-axis limits and ticks
    ax.set_ylim(0, 100)
    ax.set_yticks(range(0, 101, 20))
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{int(x)}%'))
    
    # Add grid
    ax.grid(True, alpha=0.3)
    
    # Rotate x-axis labels
    plt.xticks(rotation=45)
    
    # Tight layout
    plt.tight_layout()
    
    return fig_to_base64(fig)


def create_quiz_bar_chart(class_stats):
    """
    Create a professional quiz performance bar chart by class.
    
    Args:
        class_stats: List of class quiz statistics
        
    Returns:
        str: Base64 encoded image data
    """
    if not class_stats:
        return create_no_data_chart("Aucune donnée de quiz par classe disponible")
    
    # Limit to top 8 classes for readability
    class_stats = class_stats[:8]
    
    # Prepare data
    class_names = [stat.get('class_name', f'Classe {i+1}')[:15] for i, stat in enumerate(class_stats)]
    scores = [stat.get('avg_score', 0) for stat in class_stats]
    
    # Create figure
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # Create bars with gradient colors
    colors = plt.cm.plasma(np.linspace(0.3, 0.9, len(class_names)))
    bars = ax.bar(class_names, scores, color=colors, alpha=0.8, edgecolor='white', linewidth=1)
    
    # Add value labels on bars
    for bar, score in zip(bars, scores):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{score:.1f}%', ha='center', va='bottom', fontweight='bold')
    
    # Customize axes
    ax.set_title('Score Moyen par Classe', fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('Classes', fontsize=12, fontweight='bold')
    ax.set_ylabel('Score Moyen (%)', fontsize=12, fontweight='bold')
    
    # Set y-axis limits and ticks
    ax.set_ylim(0, 100)
    ax.set_yticks(range(0, 101, 20))
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{int(x)}%'))
    
    # Add grid
    ax.grid(True, alpha=0.3, axis='y')
    
    # Rotate x-axis labels
    plt.xticks(rotation=45, ha='right')
    
    # Tight layout
    plt.tight_layout()
    
    return fig_to_base64(fig)


def create_quiz_pie_chart(score_distribution):
    """
    Create a professional quiz score distribution pie chart.

    Args:
        score_distribution: List of score distribution data

    Returns:
        str: Base64 encoded image data
    """
    if not score_distribution:
        return create_no_data_chart("Aucune donnée de distribution disponible")

    # Prepare data
    labels = []
    sizes = []
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']

    for i, dist in enumerate(score_distribution):
        score_range = dist.get('score_range', '')
        count = dist.get('count', 0)

        if count > 0:
            labels.append(f'{score_range}\n{count} étudiants')
            sizes.append(count)

    if not sizes:
        return create_no_data_chart("Aucune donnée de distribution disponible")

    # Create figure
    fig, ax = plt.subplots(figsize=(10, 8))

    # Create pie chart
    wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors[:len(sizes)],
                                     autopct='%1.1f%%', startangle=90,
                                     textprops={'fontsize': 11, 'fontweight': 'bold'})

    # Customize
    ax.set_title('Distribution des Scores de Quiz', fontsize=16, fontweight='bold', pad=20)

    # Equal aspect ratio ensures that pie is drawn as a circle
    ax.axis('equal')

    # Tight layout
    plt.tight_layout()

    return fig_to_base64(fig)


def fig_to_base64(fig):
    """Convert matplotlib figure to base64 string."""
    buffer = io.BytesIO()
    fig.savefig(buffer, format='png', dpi=150, bbox_inches='tight',
                facecolor='white', edgecolor='none')
    buffer.seek(0)
    image_base64 = base64.b64encode(buffer.getvalue()).decode()
    plt.close(fig)
    return f"data:image/png;base64,{image_base64}"


def create_no_data_chart(message):
    """Create a simple chart showing no data message."""
    fig, ax = plt.subplots(figsize=(8, 4))
    ax.text(0.5, 0.5, message, ha='center', va='center', fontsize=14,
            transform=ax.transAxes, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.axis('off')
    plt.tight_layout()
    return fig_to_base64(fig)
