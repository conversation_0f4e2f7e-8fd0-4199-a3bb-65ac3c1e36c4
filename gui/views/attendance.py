import flet as ft
from datetime import datetime
from gui.components.layout import create_page_layout
from gui.services.attendance_service import get_attendance_records
from gui.services.class_service import get_existing_classes
from gui.services.subject_service import get_class_subjects

from gui.config.language import get_text
from gui.config.constants import ICON_ATTENDANCE, ICON_SEARCH, ICON_CLEAR

def create_attendance_view(page: ft.Page):
    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # State variables
    attendance_records = []
    filtered_records = []

    # Compact filter controls
    class_filter = ft.Dropdown(
        label=get_text("select_class", current_language),
        width=360 if not is_mobile else None,
        expand=is_mobile,
        dense=True,
        content_padding=ft.padding.symmetric(horizontal=12, vertical=8)
    )

    subject_filter = ft.Dropdown(
        label=get_text("select_subject", current_language),
        width=360 if not is_mobile else None,
        expand=is_mobile,
        dense=True,
        content_padding=ft.padding.symmetric(horizontal=12, vertical=8)
    )

    date_picker = ft.DatePicker(
        first_date=datetime(2020, 1, 1),
        last_date=datetime(2030, 12, 31),
        on_change=lambda _: load_attendance_records()
    )

    # Add date picker to page
    page.overlay.append(date_picker)

    date_filter = ft.ElevatedButton(
        text=get_text("select_date", current_language),
        icon=ft.Icons.CALENDAR_TODAY,
        width=360 if not is_mobile else None,
        expand=is_mobile,
        on_click=lambda _: page.open(date_picker),
        style=ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=8),
            bgcolor=ft.Colors.BLUE_GREY_100
        )
    )

    search_field = ft.TextField(
        hint_text=f"{get_text('search', current_language)}...",
        prefix_icon=ICON_SEARCH,
        width=360 if not is_mobile else None,
        expand=is_mobile,
        dense=True,
        content_padding=ft.padding.symmetric(horizontal=12, vertical=8)
    )

    # Records container
    records_container = ft.Column(spacing=10)

    def load_filter_options():
        try:
            # Load classes
            classes_dict = get_existing_classes()
            class_options = [ft.dropdown.Option(key="", text=get_text("all_classes", current_language))]

            # Convert dictionary to list format for dropdown options
            if isinstance(classes_dict, dict):
                for class_name, class_info in classes_dict.items():
                    if isinstance(class_info, dict) and 'id' in class_info:
                        class_options.append(ft.dropdown.Option(key=str(class_info['id']), text=class_name))

            class_filter.options = class_options

            # Load subjects based on selected class
            load_subjects_for_class()

            page.update()
        except Exception as e:
            print(f"Error loading filter options: {e}")
            # Fallback to empty options
            class_filter.options = [ft.dropdown.Option(key="", text=get_text("all_classes", current_language))]
            subject_filter.options = [ft.dropdown.Option(key="", text=get_text("all_subjects", current_language))]
            page.update()

    def load_subjects_for_class():
        """Load subjects based on selected class"""
        try:
            selected_class_id = class_filter.value if class_filter.value else None

            # Temporarily show loading state
            subject_filter.options = [ft.dropdown.Option(key="", text="Chargement...")]
            subject_filter.value = ""
            page.update()

            if selected_class_id:
                # Load subjects for specific class
                subjects = get_class_subjects(selected_class_id)
            else:
                # Load all subjects
                subjects = get_class_subjects()

            # Ensure subjects is a list
            if not isinstance(subjects, list):
                subjects = []

            subject_options = [ft.dropdown.Option(key="", text=get_text("all_subjects", current_language))]
            subject_options.extend([ft.dropdown.Option(key=str(s['id']), text=s['subject_name']) for s in subjects if s.get('id') and s.get('subject_name')])
            subject_filter.options = subject_options
            subject_filter.value = ""  # Reset subject selection when class changes
            page.update()
        except Exception as e:
            print(f"Error loading subjects for class: {e}")
            # Fallback to empty options
            subject_filter.options = [ft.dropdown.Option(key="", text=get_text("all_subjects", current_language))]
            subject_filter.value = ""
            page.update()

    def load_attendance_records():
        nonlocal attendance_records, filtered_records

        # Get filter values
        class_id = class_filter.value if class_filter.value else None
        # Fix: Handle empty string for "All Subjects" option
        subject_id = None if (not subject_filter.value or subject_filter.value == "") else subject_filter.value

        # Get selected date from date picker
        selected_date = None
        if date_picker.value:
            selected_date = date_picker.value.strftime('%Y-%m-%d')
            # Update button text to show selected date
            date_filter.text = selected_date

        # Load records
        attendance_records = get_attendance_records(
            class_id=class_id,
            subject_id=subject_id,
            date_from=selected_date,
            date_to=selected_date
        )

        # Apply search filter
        search_term = search_field.value.lower() if search_field.value else ""
        if search_term:
            filtered_records = [r for r in attendance_records if search_term in r['student_name'].lower()]
        else:
            filtered_records = attendance_records

        display_records()

    def display_records():
        records_container.controls.clear()

        if not filtered_records:
            records_container.controls.append(
                ft.Container(
                    content=ft.Text(
                        get_text("no_attendance_records", current_language),
                        size=16,
                        text_align=ft.TextAlign.CENTER,
                        color=ft.Colors.BLUE_GREY_200
                    ),
                    padding=ft.padding.all(20),
                    alignment=ft.alignment.center
                )
            )
        else:
            # Group records by date
            records_by_date = {}
            for record in filtered_records:
                date = record['date']
                if date not in records_by_date:
                    records_by_date[date] = []
                records_by_date[date].append(record)

            # Sort dates (newest first)
            sorted_dates = sorted(records_by_date.keys(), reverse=True)

            # Create table for each date
            for date in sorted_dates:
                date_records = records_by_date[date]
                records_container.controls.append(create_date_table(date, date_records))

        page.update()

    def create_date_table(date, records):
        try:
            from datetime import datetime
            date_obj = datetime.strptime(date, '%Y-%m-%d')
            formatted_date = date_obj.strftime('%B %d, %Y')
        except:
            formatted_date = date

        present_count = sum(1 for r in records if r['status'])
        absent_count = len(records) - present_count

        # Modern date header
        date_header = ft.Container(
            content=ft.Row([
                ft.Text(formatted_date, size=18, weight=ft.FontWeight.W_600),
                ft.Container(expand=True),
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.WHITE, size=18),
                                ft.Text(str(present_count), size=15, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE)
                            ], spacing=6, alignment=ft.MainAxisAlignment.CENTER),
                            bgcolor=ft.Colors.GREEN_600,
                            padding=ft.padding.symmetric(horizontal=12, vertical=8),
                            border_radius=20,
                            shadow=ft.BoxShadow(
                                spread_radius=0,
                                blur_radius=4,
                                color=ft.Colors.with_opacity(0.3, ft.Colors.GREEN_600),
                                offset=ft.Offset(0, 2)
                            )
                        ),
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.CANCEL, color=ft.Colors.WHITE, size=18),
                                ft.Text(str(absent_count), size=15, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE)
                            ], spacing=6, alignment=ft.MainAxisAlignment.CENTER),
                            bgcolor=ft.Colors.RED_600,
                            padding=ft.padding.symmetric(horizontal=12, vertical=8),
                            border_radius=20,
                            shadow=ft.BoxShadow(
                                spread_radius=0,
                                blur_radius=4,
                                color=ft.Colors.with_opacity(0.3, ft.Colors.RED_600),
                                offset=ft.Offset(0, 2)
                            )
                        )
                    ], spacing=12)
                )
            ]),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.PRIMARY_CONTAINER,
            border_radius=8
        )

        # Modern table with improved French design
        table_data = []
        for i, record in enumerate(records):
            # Enhanced status display with better colors and icons
            if record['status']:
                status_container = ft.Container(
                    content=ft.Row([
                        ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.WHITE, size=16),
                        ft.Text("Présent", color=ft.Colors.WHITE, size=12, weight=ft.FontWeight.W_600)
                    ], spacing=6, alignment=ft.MainAxisAlignment.CENTER),
                    bgcolor=ft.Colors.GREEN_500,
                    padding=ft.padding.symmetric(horizontal=12, vertical=6),
                    border_radius=16,
                    width=100
                )
            else:
                status_container = ft.Container(
                    content=ft.Row([
                        ft.Icon(ft.Icons.CANCEL, color=ft.Colors.WHITE, size=16),
                        ft.Text("Absent", color=ft.Colors.WHITE, size=12, weight=ft.FontWeight.W_600)
                    ], spacing=6, alignment=ft.MainAxisAlignment.CENTER),
                    bgcolor=ft.Colors.RED_500,
                    padding=ft.padding.symmetric(horizontal=12, vertical=6),
                    border_radius=16,
                    width=100
                )

            # Enhanced row design with better spacing and typography
            table_data.append(
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            content=ft.Row([
                                ft.Container(
                                    content=ft.Icon(ft.Icons.PERSON, size=20, color=ft.Colors.BLUE_600),
                                    width=32,
                                    height=32,
                                    bgcolor=ft.Colors.BLUE_50,
                                    border_radius=16,
                                    alignment=ft.alignment.center
                                ),
                                ft.Text(
                                    record['student_name'],
                                    size=15,
                                    weight=ft.FontWeight.W_600,
                                    color=ft.Colors.BLUE_900
                                )
                            ], spacing=12),
                            expand=3
                        ),
                        ft.Container(
                            content=ft.Text(
                                record['class_name'],
                                size=14,
                                color=ft.Colors.GREY_700,
                                weight=ft.FontWeight.W_500
                            ),
                            expand=2
                        ),
                        ft.Container(
                            content=ft.Text(
                                record['subject_name'],
                                size=14,
                                color=ft.Colors.GREY_700,
                                weight=ft.FontWeight.W_500
                            ),
                            expand=2
                        ),
                        ft.Container(
                            content=ft.Text(
                                record['time'],
                                size=14,
                                color=ft.Colors.GREY_600,
                                weight=ft.FontWeight.W_400
                            ),
                            expand=2
                        ),
                        ft.Container(
                            content=status_container,
                            alignment=ft.alignment.center
                        )
                    ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                    padding=ft.padding.symmetric(horizontal=20, vertical=16),
                    bgcolor=ft.Colors.WHITE if i % 2 == 0 else ft.Colors.BLUE_50,
                    border_radius=12,
                    margin=ft.margin.symmetric(vertical=2),
                    shadow=ft.BoxShadow(
                        spread_radius=0,
                        blur_radius=4,
                        color=ft.Colors.with_opacity(0.05, ft.Colors.BLACK),
                        offset=ft.Offset(0, 2)
                    ) if i % 2 == 0 else None,
                    border=ft.border.all(1, ft.Colors.BLUE_100) if i % 2 == 0 else None
                )
            )

            # Add line separator between rows (except for last row)
            if i < len(records) - 1:
                table_data.append(
                    ft.Container(
                        height=1,
                        bgcolor=ft.Colors.OUTLINE_VARIANT,
                        margin=ft.margin.symmetric(horizontal=8)
                    )
                )

        return ft.Container(
            content=ft.Column([
                date_header,
                ft.Container(
                    content=ft.Column(table_data, spacing=4),
                    padding=ft.padding.all(16),
                    border_radius=16,
                    bgcolor=ft.Colors.GREY_50,
                    shadow=ft.BoxShadow(
                        spread_radius=0,
                        blur_radius=8,
                        color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                        offset=ft.Offset(0, 4)
                    ),
                    border=ft.border.all(1, ft.Colors.BLUE_100)
                )
            ], spacing=16),
            margin=ft.margin.only(bottom=20)
        )



    def export_to_csv():
        if not filtered_records:
            snack_bar = ft.SnackBar(
                content=ft.Text("Aucune donnée à exporter!"),
                action="d'accord",
                bgcolor=ft.Colors.RED_600,
                action_color=ft.Colors.WHITE
            )
            page.open(snack_bar)
            page.update()
            return

        import csv
        import os
        from datetime import datetime

        # Create CSV content
        csv_content = []
        csv_content.append(['Student Name', 'Class', 'Subject', 'Date', 'Time', 'Status'])

        for record in filtered_records:
            status_text = 'Present' if record['status'] else 'Absent'
            csv_content.append([
                record['student_name'],
                record['class_name'],
                record['subject_name'],
                record['date'],
                record['time'],
                status_text
            ])

        # Create filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"attendance_export_{timestamp}.csv"

        # Save to Downloads folder or current directory
        downloads_path = os.path.expanduser("~/Downloads")
        if os.path.exists(downloads_path):
            file_path = os.path.join(downloads_path, filename)
        else:
            file_path = filename

        try:
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerows(csv_content)

            # Show success message
            snack_bar = ft.SnackBar(
                content=ft.Text(f"Données exportées vers: {file_path}"), # "Attendance data exported to: {file_path}"
                action="d'accord",
                bgcolor=ft.Colors.GREEN_600,
                action_color=ft.Colors.WHITE,
            )
            page.open(snack_bar)
            page.update()
            print(f"✅ Attendance data exported to: {file_path}")
        except Exception as e:
            # Show error message
            snack_bar = ft.SnackBar(
                content=ft.Text(f"Erreur lors de l'export: {str(e)}"),
                action="d'accord",
                bgcolor=ft.Colors.RED_600,
                action_color=ft.Colors.WHITE
            )
            page.open(snack_bar)
            page.update()



    def on_class_filter_change(_):
        load_subjects_for_class()  # Reload subjects when class changes
        load_attendance_records()

    def on_filter_change(_):
        load_attendance_records()

    def on_search_change(_):
        load_attendance_records()

    def clear_filters(_):
        class_filter.value = ""
        subject_filter.value = ""
        date_picker.value = None
        date_filter.text = get_text("select_date", current_language)
        search_field.value = ""
        load_subjects_for_class()  # Reload subjects when clearing filters
        load_attendance_records()

    # Set up event handlers
    class_filter.on_change = on_class_filter_change
    subject_filter.on_change = on_filter_change
    search_field.on_change = on_search_change

    # Enhanced filter section with better design for desktop
    if not is_mobile:
        filter_section = ft.Container(
            content=ft.Column([
                # Header with title and filter count
                ft.Row([
                    ft.Text(
                        get_text("filter_attendance", current_language),
                        size=18,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.PRIMARY
                    ),
                    ft.Container(expand=True),
                    ft.Container(
                        content=ft.Text(
                            "Filtres Avancés",
                            size=12,
                            color=ft.Colors.BLUE_600,
                            weight=ft.FontWeight.W_500
                        ),
                        padding=ft.padding.symmetric(horizontal=8, vertical=4),
                        bgcolor=ft.Colors.BLUE_50,
                        border_radius=12
                    )
                ]),
                ft.Divider(height=1, color=ft.Colors.OUTLINE_VARIANT),
                # Main filters row
                ft.Row([
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Classe", size=12, weight=ft.FontWeight.W_500, color=ft.Colors.BLUE_GREY_600),
                            class_filter
                        ], spacing=4),
                        expand=2
                    ),
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Matière", size=12, weight=ft.FontWeight.W_500, color=ft.Colors.BLUE_GREY_600),
                            subject_filter
                        ], spacing=4),
                        expand=2
                    ),
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Date", size=12, weight=ft.FontWeight.W_500, color=ft.Colors.BLUE_GREY_600),
                            date_filter
                        ], spacing=4),
                        expand=2
                    ),
                    ft.Container(
                        content=ft.Column([
                            ft.Text("Recherche", size=12, weight=ft.FontWeight.W_500, color=ft.Colors.BLUE_GREY_600),
                            search_field
                        ], spacing=4),
                        expand=2
                    )
                ], spacing=16),
                # Action buttons
                ft.Row([
                    ft.ElevatedButton(
                        get_text("clear_filters", current_language),
                        on_click=clear_filters,
                        icon=ICON_CLEAR,
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=10),
                            bgcolor=ft.Colors.BLUE_GREY_100,
                            color=ft.Colors.BLUE_GREY_700
                        )
                    ),
                    ft.Container(expand=True),
                    ft.ElevatedButton(
                        "Exporter CSV",
                        on_click=lambda _: export_to_csv(),
                        icon=ft.Icons.DOWNLOAD,
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=10),
                            bgcolor=ft.Colors.GREEN_600,
                            color=ft.Colors.WHITE
                        )
                    )
                ], spacing=12)
            ], spacing=16),
            padding=ft.padding.all(24),
            margin=ft.margin.only(bottom=24),
            bgcolor=ft.Colors.SURFACE,
            border_radius=ft.border_radius.all(16),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=8,
                color=ft.Colors.with_opacity(0.12, ft.Colors.BLACK),
                offset=ft.Offset(0, 2)
            ),
            border=ft.border.all(1, ft.Colors.OUTLINE_VARIANT)
        )
    else:
        # Keep mobile version unchanged
        filter_section = ft.Container(
            content=ft.Column([
                ft.Text(
                    get_text("filter_attendance", current_language),
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.ON_SURFACE
                ),
                ft.Column([class_filter, subject_filter], spacing=12),
                ft.Column([date_filter, search_field], spacing=12),
                ft.Row([
                    ft.ElevatedButton(
                        get_text("clear_filters", current_language),
                        on_click=clear_filters,
                        icon=ICON_CLEAR,
                        expand=True
                    ),
                    ft.ElevatedButton(
                        "Exporter CSV",
                        on_click=lambda _: export_to_csv(),
                        icon=ft.Icons.DOWNLOAD,
                        expand=True,
                        style=ft.ButtonStyle(
                            bgcolor=ft.Colors.GREEN_600,
                            color=ft.Colors.WHITE
                        )
                    )
                ], spacing=8)
            ], spacing=12),
            width=page.width*0.9,
            padding=ft.padding.all(20),
            margin=ft.margin.only(bottom=20),
            bgcolor=ft.Colors.SURFACE,
            border_radius=ft.border_radius.all(12),
            shadow=ft.BoxShadow(
                spread_radius=0,
                blur_radius=4,
                color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
                offset=ft.Offset(0, 1)
            ),
        )

    # Records section
    records_section = ft.Container(
        content=ft.Column([records_container], scroll=ft.ScrollMode.AUTO),
        expand=True,
        padding=ft.padding.all(16),
        bgcolor=ft.Colors.SURFACE,
        border_radius=12
    )

    # Main content
    content = ft.Column([
        filter_section,
        records_section
    ], spacing=0, expand=True)

    # Initialize data
    load_filter_options()
    load_attendance_records()

    return create_page_layout(
        page,
        get_text("attendance_management", current_language),
        content,
        ICON_ATTENDANCE
    )

def create_class_attendance_view(page: ft.Page, class_id, class_name):
    current_language = getattr(page, 'language', 'en')
    is_mobile = getattr(page, 'is_mobile', False)

    # State variables
    attendance_records = []
    filtered_records = []

    # Compact filter controls
    subject_filter = ft.Dropdown(
        label=get_text("select_subject", current_language),
        width=360 if not is_mobile else None,
        expand=True,
        dense=True,
        content_padding=ft.padding.symmetric(horizontal=12, vertical=8)
    )

    date_picker = ft.DatePicker(
        first_date=datetime(2020, 1, 1),
        last_date=datetime(2030, 12, 31),
        on_change=lambda _: load_attendance_records()
    )

    # Add date picker to page
    page.overlay.append(date_picker)

    date_filter = ft.ElevatedButton(
        text=get_text("select_date", current_language),
        icon=ft.Icons.CALENDAR_TODAY,
        width=360 if not is_mobile else None,
        expand=is_mobile,
        on_click=lambda _: page.open(date_picker),
        style=ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=8),
            bgcolor=ft.Colors.BLUE_GREY_100
        )
    )

    search_field = ft.TextField(
        hint_text=f"{get_text('search', current_language)}...",
        prefix_icon=ICON_SEARCH,
        width=360 if not is_mobile else None,
        expand=is_mobile,
        dense=True,
        content_padding=ft.padding.symmetric(horizontal=12, vertical=8)
    )

    sort_dropdown = ft.Dropdown(
        label="trier par",
        # width=120 if not is_mobile else None,
        expand=True,
        dense=True,
        content_padding=ft.padding.symmetric(horizontal=12, vertical=8),
        options=[
            ft.dropdown.Option(key="date_desc", text="les plus récents"),
            ft.dropdown.Option(key="date_asc", text="les plus anciens"),
            ft.dropdown.Option(key="name_asc", text="nom A-Z"),
            ft.dropdown.Option(key="name_desc", text="nom Z-A"),
            ft.dropdown.Option(key="status_present", text="présents en premier"),
            ft.dropdown.Option(key="status_absent", text="absents en premier")
        ],
        value="date_desc"
    )

    # Records container
    records_container = ft.Column(spacing=10)

    def load_filter_options():
        try:
            # Load subjects for this specific class
            subjects = get_class_subjects(class_id)

            # Ensure subjects is a list
            if not isinstance(subjects, list):
                subjects = []

            subject_options = [ft.dropdown.Option(key="", text=get_text("all_subjects", current_language))]
            subject_options.extend([ft.dropdown.Option(key=str(s['id']), text=s['subject_name']) for s in subjects if s.get('id') and s.get('subject_name')])
            subject_filter.options = subject_options
            page.update()
        except Exception as e:
            print(f"Error loading filter options for class {class_id}: {e}")
            # Fallback to empty options
            subject_filter.options = [ft.dropdown.Option(key="", text=get_text("all_subjects", current_language))]
            page.update()



    def sort_records(records, sort_by):
        if sort_by == "date_desc":
            return sorted(records, key=lambda x: (x['date'], x['time']), reverse=True)
        elif sort_by == "date_asc":
            return sorted(records, key=lambda x: (x['date'], x['time']))
        elif sort_by == "name_asc":
            return sorted(records, key=lambda x: x['student_name'].lower())
        elif sort_by == "name_desc":
            return sorted(records, key=lambda x: x['student_name'].lower(), reverse=True)
        elif sort_by == "status_present":
            return sorted(records, key=lambda x: (not x['status'], x['student_name'].lower()))
        elif sort_by == "status_absent":
            return sorted(records, key=lambda x: (x['status'], x['student_name'].lower()))
        return records

    def load_attendance_records():
        nonlocal attendance_records, filtered_records

        # Get filter values (always filter by class_id)
        # Fix: Handle empty string for "All Subjects" option
        subject_id = None if (not subject_filter.value or subject_filter.value == "") else subject_filter.value

        # Get selected date from date picker
        selected_date = None
        if date_picker.value:
            selected_date = date_picker.value.strftime('%Y-%m-%d')
            # Update button text to show selected date
            date_filter.text = selected_date

        # Load records for this specific class
        attendance_records = get_attendance_records(
            class_id=class_id,
            subject_id=subject_id,
            date_from=selected_date,
            date_to=selected_date
        )

        # Apply search filter
        search_term = search_field.value.lower() if search_field.value else ""
        if search_term:
            filtered_records = [r for r in attendance_records if search_term in r['student_name'].lower()]
        else:
            filtered_records = attendance_records

        # Apply sorting
        current_sort = sort_dropdown.value if sort_dropdown.value else "date_desc"
        filtered_records = sort_records(filtered_records, current_sort)

        display_records()

    def display_records():
        records_container.controls.clear()

        if not filtered_records:
            # Enhanced empty state with better design
            empty_state = ft.Container(
                content=ft.Column([
                    ft.Icon(
                        ft.Icons.ASSIGNMENT_OUTLINED,
                        size=64,
                        color=ft.Colors.BLUE_GREY_200
                    ),
                    ft.Text(
                        get_text("no_attendance_records", current_language),
                        size=18,
                        weight=ft.FontWeight.W_500,
                        text_align=ft.TextAlign.CENTER,
                        color=ft.Colors.BLUE_GREY_200
                    ),
                    ft.Text(
                        "Essayez de modifier vos filtres ou la plage de dates",
                        size=14,
                        text_align=ft.TextAlign.CENTER,
                        color=ft.Colors.BLUE_GREY_200
                    )
                ], spacing=16, horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                padding=ft.padding.all(40),
                alignment=ft.alignment.center
            )
            records_container.controls.append(empty_state)
        else:
            # Add records count header
            count_header = ft.Container(
                content=ft.Row([
                    ft.Container(expand=True),
                    ft.ElevatedButton(
                        "Exporter CSV",
                        on_click=lambda _: export_class_csv(),
                        icon=ft.Icons.DOWNLOAD,
                        style=ft.ButtonStyle(
                            shape=ft.RoundedRectangleBorder(radius=8),
                            bgcolor=ft.Colors.GREEN_600,
                            color=ft.Colors.WHITE
                        )
                    )
                ]),
                padding=ft.padding.symmetric(horizontal=16, vertical=8),
                margin=ft.margin.only(bottom=8)
            )
            records_container.controls.append(count_header)

            # Group records by date
            records_by_date = {}
            for record in filtered_records:
                date = record['date']
                if date not in records_by_date:
                    records_by_date[date] = []
                records_by_date[date].append(record)

            # Sort dates (newest first)
            sorted_dates = sorted(records_by_date.keys(), reverse=True)

            # Create table for each date
            for date in sorted_dates:
                date_records = records_by_date[date]
                records_container.controls.append(create_class_date_table(date, date_records))

        page.update()

    def create_class_date_table(date, records):
        try:
            from datetime import datetime
            date_obj = datetime.strptime(date, '%Y-%m-%d')
            formatted_date = date_obj.strftime('%B %d, %Y')
        except:
            formatted_date = date

        present_count = sum(1 for r in records if r['status'])
        absent_count = len(records) - present_count

        # Modern date header
        date_header = ft.Container(
            content=ft.Row([
                ft.Text(formatted_date, size=18, weight=ft.FontWeight.W_600),
                ft.Container(expand=True),
                ft.Container(
                    content=ft.Row([
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.WHITE, size=18),
                                ft.Text(str(present_count), size=15, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE)
                            ], spacing=6, alignment=ft.MainAxisAlignment.CENTER),
                            bgcolor=ft.Colors.GREEN_600,
                            padding=ft.padding.symmetric(horizontal=12, vertical=8),
                            border_radius=20,
                            shadow=ft.BoxShadow(
                                spread_radius=0,
                                blur_radius=4,
                                color=ft.Colors.with_opacity(0.3, ft.Colors.GREEN_600),
                                offset=ft.Offset(0, 2)
                            )
                        ),
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.CANCEL, color=ft.Colors.WHITE, size=18),
                                ft.Text(str(absent_count), size=15, weight=ft.FontWeight.BOLD, color=ft.Colors.WHITE)
                            ], spacing=6, alignment=ft.MainAxisAlignment.CENTER),
                            bgcolor=ft.Colors.RED_600,
                            padding=ft.padding.symmetric(horizontal=12, vertical=8),
                            border_radius=20,
                            shadow=ft.BoxShadow(
                                spread_radius=0,
                                blur_radius=4,
                                color=ft.Colors.with_opacity(0.3, ft.Colors.RED_600),
                                offset=ft.Offset(0, 2)
                            )
                        )
                    ], spacing=12)
                )
            ]),
            padding=ft.padding.all(16),
            bgcolor=ft.Colors.PRIMARY_CONTAINER,
            border_radius=8
        )

        # Modern table with borders
        table_data = []
        for i, record in enumerate(records):
            status_icon = ft.Icon(ft.Icons.CHECK_CIRCLE, color=ft.Colors.GREEN, size=20) if record['status'] else ft.Icon(ft.Icons.CANCEL, color=ft.Colors.RED, size=20)

            table_data.append(
                ft.Container(
                    content=ft.Row([
                        ft.Text(record['student_name'], size=14, weight=ft.FontWeight.W_500, expand=3),
                        ft.Text(record['subject_name'], size=13, expand=2),
                        ft.Text(record['time'], size=13, expand=2),
                        status_icon
                    ]),
                    padding=ft.padding.symmetric(horizontal=16, vertical=12),
                    bgcolor=ft.Colors.BLUE_GREY_200 if i % 2 == 0 else ft.Colors.SURFACE,
                    border=ft.border.all(1, ft.Colors.OUTLINE_VARIANT),
                    border_radius=4
                )
            )

            # Add line separator between rows (except for last row)
            if i < len(records) - 1:
                table_data.append(
                    ft.Container(
                        height=1,
                        bgcolor=ft.Colors.OUTLINE_VARIANT,
                        margin=ft.margin.symmetric(horizontal=8)
                    )
                )

        return ft.Container(
            content=ft.Column([
                date_header,
                ft.Container(
                    content=ft.Column(table_data, spacing=0),
                    border=ft.border.all(1, ft.Colors.OUTLINE_VARIANT),
                    border_radius=8,
                    bgcolor=ft.Colors.SURFACE
                )
            ], spacing=12),
            margin=ft.margin.only(bottom=16)
        )



    def export_class_csv():
        if not filtered_records:
            snack_bar = ft.SnackBar(
                content=ft.Text("Aucune donnée à exporter!"),
                action="d'accord",
                bgcolor=ft.Colors.RED_600,
                action_color=ft.Colors.WHITE
            )
            page.open(snack_bar)
            page.update()
            return

        import csv
        import os
        from datetime import datetime

        # Create CSV content
        csv_content = []
        csv_content.append(['Student Name', 'Subject', 'Date', 'Time', 'Status'])

        for record in filtered_records:
            status_text = 'Present' if record['status'] else 'Absent'
            csv_content.append([
                record['student_name'],
                record['subject_name'],
                record['date'],
                record['time'],
                status_text
            ])

        # Create filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"class_attendance_export_{timestamp}.csv"

        # Save to Downloads folder or current directory
        downloads_path = os.path.expanduser("~/Downloads")
        if os.path.exists(downloads_path):
            file_path = os.path.join(downloads_path, filename)
        else:
            file_path = filename

        try:
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerows(csv_content)

            # Show success message
            snack_bar = ft.SnackBar(
                content=ft.Text(f"Données exportées vers: {file_path}"), # "Attendance data exported to: {file_path}"
                action="d'accord",
                bgcolor=ft.Colors.GREEN_600,
                action_color=ft.Colors.WHITE,
            )
            page.open(snack_bar)
            page.update()
            print(f"✅ Attendance data exported to: {file_path}")
        except Exception as e:
            # Show error message
            snack_bar = ft.SnackBar(
                content=ft.Text(f"Erreur lors de l'export: {str(e)}"),
                action="d'accord",
                bgcolor=ft.Colors.RED_600,
                action_color=ft.Colors.WHITE
            )
            page.open(snack_bar)
            page.update()



    def on_filter_change(_):
        load_attendance_records()

    def on_search_change(_):
        load_attendance_records()

    def on_sort_change(_):
        load_attendance_records()

    def clear_filters(_):
        subject_filter.value = ""
        date_picker.value = None
        date_filter.text = get_text("select_date", current_language)
        search_field.value = ""
        sort_dropdown.value = "date_desc"
        load_attendance_records()

    # Set up event handlers
    subject_filter.on_change = on_filter_change
    search_field.on_change = on_search_change
    sort_dropdown.on_change = on_sort_change

    # Modern filter section with consistent design
    filter_section = ft.Container(
        content=ft.Column([
            ft.Text(
                get_text("filter_attendance", current_language),
                size=16,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.ON_SURFACE
            ),
            ft.Row([
                subject_filter,
                sort_dropdown,
            ], spacing=12) if not is_mobile else ft.Column([subject_filter, sort_dropdown], spacing=12),
            ft.Row([
                date_filter,
                search_field,
            ], spacing=12) if not is_mobile else ft.Column([date_filter, search_field], spacing=12),
            ft.Row([
                ft.ElevatedButton(
                    get_text("clear_filters", current_language),
                    on_click=clear_filters,
                    icon=ICON_CLEAR,
                    style=ft.ButtonStyle(
                        shape=ft.RoundedRectangleBorder(radius=8)
                    )
                )
            ], spacing=12)
        ], spacing=12),
        width=page.width*0.9 if is_mobile else None,
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20),
        bgcolor=ft.Colors.SURFACE,
        border_radius=ft.border_radius.all(12),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=4,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 1)
        ),
    )

    # Records section
    records_section = ft.Container(
        content=ft.Column([records_container], scroll=ft.ScrollMode.ALWAYS),
        expand=True,
        padding=ft.padding.all(16),
        bgcolor=ft.Colors.SURFACE,
        border_radius=12
    )

    # Main content
    content = ft.Column([
        filter_section,
        records_section
    ], spacing=0, expand=True)

    # Initialize data
    load_filter_options()
    load_attendance_records()

    return content
