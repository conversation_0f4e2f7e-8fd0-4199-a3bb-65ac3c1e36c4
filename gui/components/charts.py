"""
Chart components for displaying statistics in the admin interface using matplotlib.
"""
import flet as ft
from gui.services.matplotlib_charts import (
    create_attendance_line_chart as mpl_attendance_line,
    create_attendance_bar_chart as mpl_attendance_bar,
    create_attendance_pie_chart as mpl_attendance_pie,
    create_quiz_line_chart as mpl_quiz_line,
    create_quiz_bar_chart as mpl_quiz_bar,
    create_quiz_pie_chart as mpl_quiz_pie
)


def create_attendance_line_chart(daily_stats, period_label="30 jours"):
    """
    Create a professional attendance line chart using matplotlib.

    Args:
        daily_stats: List of daily attendance statistics
        period_label: Label for the time period

    Returns:
        ft.Image: Image component with matplotlib chart
    """
    # Generate chart using matplotlib
    chart_data = mpl_attendance_line(daily_stats, period_label)

    return ft.Image(
        src=chart_data,
        width=800,
        height=400,
        fit=ft.ImageFit.CONTAIN,
    )


def create_attendance_bar_chart(class_stats):
    """
    Create a professional attendance bar chart using matplotlib.

    Args:
        class_stats: List of class attendance statistics

    Returns:
        ft.Image: Image component with matplotlib chart
    """
    # Generate chart using matplotlib
    chart_data = mpl_attendance_bar(class_stats)

    return ft.Image(
        src=chart_data,
        width=800,
        height=400,
        fit=ft.ImageFit.CONTAIN,
    )


def create_attendance_pie_chart(overall_stats):
    """
    Create a professional attendance pie chart using matplotlib.

    Args:
        overall_stats: Overall attendance statistics

    Returns:
        ft.Image: Image component with matplotlib chart
    """
    # Generate chart using matplotlib
    chart_data = mpl_attendance_pie(overall_stats)

    return ft.Image(
        src=chart_data,
        width=600,
        height=400,
        fit=ft.ImageFit.CONTAIN,
    )


def create_chart_container(title, chart_widget, subtitle=None):
    """
    Create a container for charts with title and styling.
    
    Args:
        title: Chart title
        chart_widget: The chart widget
        subtitle: Optional subtitle
        
    Returns:
        ft.Container: Styled chart container
    """
    content = [
        ft.Text(
            title,
            size=18,
            weight=ft.FontWeight.BOLD,
            text_align=ft.TextAlign.CENTER,
        )
    ]
    
    if subtitle:
        content.append(
            ft.Text(
                subtitle,
                size=12,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.CENTER,
            )
        )
    
    content.append(chart_widget)
    
    return ft.Container(
        content=ft.Column(
            content,
            alignment=ft.MainAxisAlignment.START,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=10,
        ),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(12),
        padding=ft.padding.all(20),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=10,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 4)
        ),
        margin=ft.margin.all(10),
    )


def create_stats_summary_card(title, value, subtitle=None, icon=None, color=None):
    """
    Create a modern, simple summary statistics card.

    Args:
        title: Card title
        value: Main value to display
        subtitle: Optional subtitle
        icon: Optional icon
        color: Optional color theme

    Returns:
        ft.Container: Statistics card
    """
    color = color or ft.Colors.BLUE_600

    # Create icon with background circle
    icon_container = ft.Container(
        content=ft.Icon(icon, size=24, color=ft.Colors.WHITE),
        bgcolor=color,
        border_radius=ft.border_radius.all(25),
        width=50,
        height=50,
        alignment=ft.alignment.center,
    ) if icon else None

    # Main content layout
    content = []

    # Top row with icon and value
    top_row = []
    if icon_container:
        top_row.append(icon_container)

    # Handle None values properly
    display_value = value if value is not None else 0
    if isinstance(display_value, float):
        display_value = f"{display_value:.1f}" if display_value != int(display_value) else str(int(display_value))

    value_text = ft.Text(
        str(display_value),
        size=32,
        weight=ft.FontWeight.BOLD,
        color=ft.Colors.GREY_800,
    )
    top_row.append(value_text)

    if len(top_row) > 1:
        content.append(
            ft.Row(
                top_row,
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
            )
        )
    else:
        content.append(value_text)

    # Title
    content.append(
        ft.Text(
            title,
            size=16,
            weight=ft.FontWeight.W_500,
            color=ft.Colors.GREY_700,
        )
    )

    # Subtitle if provided
    if subtitle:
        content.append(
            ft.Text(
                subtitle,
                size=13,
                color=ft.Colors.GREY_500,
            )
        )

    return ft.Container(
        content=ft.Column(
            content,
            alignment=ft.MainAxisAlignment.START,
            horizontal_alignment=ft.CrossAxisAlignment.START,
            spacing=12,
        ),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(16),
        padding=ft.padding.all(24),
        border=ft.border.all(1, ft.Colors.with_opacity(0.1, ft.Colors.GREY)),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=20,
            color=ft.Colors.with_opacity(0.08, ft.Colors.BLACK),
            offset=ft.Offset(0, 8)
        ),
        width=280,
        height=140,
    )


def create_percentage_card(title, percentage, total_count=None, icon=None, color=None):
    """
    Create a modern percentage display card.

    Args:
        title: Card title
        percentage: Percentage value
        total_count: Optional total count to display
        icon: Optional icon
        color: Optional color theme

    Returns:
        ft.Container: Percentage card
    """
    color = color or ft.Colors.BLUE_600

    # Handle None values properly
    display_percentage = percentage if percentage is not None else 0
    if isinstance(display_percentage, float):
        display_percentage = round(display_percentage, 1)

    # Create circular progress indicator
    progress_circle = ft.Container(
        content=ft.Stack([
            ft.Container(
                width=60,
                height=60,
                border_radius=ft.border_radius.all(30),
                bgcolor=ft.Colors.with_opacity(0.1, color),
            ),
            ft.Container(
                content=ft.Text(
                    f"{display_percentage}%",
                    size=14,
                    weight=ft.FontWeight.BOLD,
                    color=color,
                ),
                width=60,
                height=60,
                alignment=ft.alignment.center,
            ),
        ]),
        width=60,
        height=60,
    )

    # Content layout
    content = [
        ft.Row([
            progress_circle,
            ft.Column([
                ft.Text(
                    title,
                    size=16,
                    weight=ft.FontWeight.W_500,
                    color=ft.Colors.GREY_700,
                ),
                ft.Text(
                    f"{total_count} total" if total_count else "",
                    size=12,
                    color=ft.Colors.GREY_500,
                ) if total_count else ft.Container(),
            ], spacing=4, expand=True)
        ], alignment=ft.MainAxisAlignment.START, spacing=16)
    ]

    return ft.Container(
        content=ft.Column(
            content,
            alignment=ft.MainAxisAlignment.CENTER,
            spacing=8,
        ),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(16),
        padding=ft.padding.all(20),
        border=ft.border.all(1, ft.Colors.with_opacity(0.1, ft.Colors.GREY)),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=20,
            color=ft.Colors.with_opacity(0.08, ft.Colors.BLACK),
            offset=ft.Offset(0, 8)
        ),
        width=280,
        height=100,
    )


def create_compact_stat_card(title, value, change=None, icon=None, color=None):
    """
    Create a compact statistics card for smaller displays.

    Args:
        title: Card title
        value: Main value to display
        change: Optional change indicator (e.g., "+5%")
        icon: Optional icon
        color: Optional color theme

    Returns:
        ft.Container: Compact statistics card
    """
    color = color or ft.Colors.BLUE_600

    content = [
        ft.Row([
            ft.Icon(icon, size=20, color=color) if icon else ft.Container(),
            ft.Text(
                title,
                size=14,
                weight=ft.FontWeight.W_500,
                color=ft.Colors.GREY_600,
                expand=True,
            ),
        ], spacing=8),
        ft.Row([
            ft.Text(
                str(value if value is not None else 0),
                size=24,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.GREY_800,
            ),
            ft.Text(
                change or "",
                size=12,
                weight=ft.FontWeight.W_500,
                color=ft.Colors.GREEN_600 if change and change.startswith('+') else ft.Colors.RED_600,
            ) if change else ft.Container(),
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
    ]

    return ft.Container(
        content=ft.Column(
            content,
            spacing=8,
        ),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(12),
        padding=ft.padding.all(16),
        border=ft.border.all(1, ft.Colors.with_opacity(0.1, ft.Colors.GREY)),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=15,
            color=ft.Colors.with_opacity(0.06, ft.Colors.BLACK),
            offset=ft.Offset(0, 4)
        ),
        width=200,
        height=80,
    )


def create_quiz_score_line_chart(daily_stats, period_label="30 jours"):
    """
    Create a professional quiz line chart using matplotlib.

    Args:
        daily_stats: List of daily quiz statistics
        period_label: Label for the time period

    Returns:
        ft.Image: Image component with matplotlib chart
    """
    # Generate chart using matplotlib
    chart_data = mpl_quiz_line(daily_stats, period_label)

    return ft.Image(
        src=chart_data,
        width=800,
        height=400,
        fit=ft.ImageFit.CONTAIN,
    )


def create_quiz_performance_bar_chart(class_stats):
    """
    Create a professional quiz bar chart using matplotlib.

    Args:
        class_stats: List of class quiz statistics

    Returns:
        ft.Image: Image component with matplotlib chart
    """
    # Generate chart using matplotlib
    chart_data = mpl_quiz_bar(class_stats)

    return ft.Image(
        src=chart_data,
        width=800,
        height=400,
        fit=ft.ImageFit.CONTAIN,
    )


def create_quiz_score_distribution_pie_chart(score_distribution):
    """
    Create a professional quiz pie chart using matplotlib.

    Args:
        score_distribution: List of score distribution data

    Returns:
        ft.Image: Image component with matplotlib chart
    """
    # Generate chart using matplotlib
    chart_data = mpl_quiz_pie(score_distribution)

    return ft.Image(
        src=chart_data,
        width=600,
        height=400,
        fit=ft.ImageFit.CONTAIN,
    )
